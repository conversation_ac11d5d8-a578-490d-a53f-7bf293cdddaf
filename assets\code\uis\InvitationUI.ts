const { ccclass, property } = cc._decorator;
import UIbase from '../utils/UIbase';
import PrefabUtil from '../utils/manager/PrefabUtil';
import AudioMgr from '../manager/AudioMgr';
import AudioPath from '../datas/AudioPath';
import LocalData from '../manager/LocalData';
import InvitationCodes from '../datas/InvitationCodes';

@ccclass
export default class InvitationUI extends UIbase {

    @property(cc.EditBox)
    input: cc.EditBox = null; // 邀请码输入框

    @property(cc.Button)
    checkmark: cc.Button = null; // 确认按钮

    @property(cc.Button)
    btn_close: cc.Button = null; // 关闭按钮

    @property(cc.Label)
    messageLabel: cc.Label = null; // 消息显示标签

    @property(cc.Node)
    panel: cc.Node = null; // 弹窗主体

    private static _inst: InvitationUI;

    public static get inst() {
        console.log("=== InvitationUI.inst 被调用 ===");

        if (this._inst == null || this._inst.node == null) {
            console.log("需要创建新的InvitationUI实例");

            let prefab = PrefabUtil.get("InvitationUI");
            console.log("获取到的预制体:", prefab);

            if (!prefab) {
                console.error("❌ InvitationUI预制体未找到");
                return null;
            }

            console.log("开始实例化预制体");
            let v = cc.instantiate(prefab);
            console.log("预制体实例化完成，节点:", v);

            console.log("获取InvitationUI组件");
            this._inst = v.getComponent(InvitationUI);
            console.log("组件获取结果:", this._inst);

            if (!this._inst) {
                console.error("❌ 无法获取InvitationUI组件，请检查预制体是否正确添加了脚本组件");
                return null;
            }
        }

        console.log("返回InvitationUI实例:", this._inst);
        return this._inst;
    }

    start() {
        this.initUI();
    }

    private initUI() {
        console.log("=== 初始化输入框UI ===");
        console.log("当前平台:", cc.sys.isMobile ? "移动端" : "PC端");

        // 初始化输入框
        if (this.input) {
            console.log("输入框组件存在，开始初始化");
            this.input.string = "";
            this.input.placeholder = "请输入6位邀请码";
            this.input.maxLength = 6;

            // 手机端特殊设置
            if (cc.sys.isMobile) {
                console.log("配置移动端输入框");
                // 设置为单行文本输入（使用数字常量）
                this.input.inputMode = 0; // SINGLE_LINE
                this.input.inputFlag = 0; // DEFAULT
                this.input.returnType = 0; // DEFAULT

                // 添加触摸事件，确保能激活输入
                this.input.node.on(cc.Node.EventType.TOUCH_START, this.onInputTouch, this);
            } else {
                console.log("配置PC端输入框");
                // PC端设置
                this.input.inputMode = 1; // ANY
                this.input.inputFlag = 0; // DEFAULT
                this.input.returnType = 0; // DEFAULT

                // PC端添加多种点击事件，确保能激活输入框
                this.input.node.on(cc.Node.EventType.TOUCH_START, this.onInputTouchPC, this);
                this.input.node.on(cc.Node.EventType.TOUCH_END, this.onInputTouchPC, this);
                this.input.node.on(cc.Node.EventType.MOUSE_DOWN, this.onInputTouchPC, this);
                this.input.node.on(cc.Node.EventType.MOUSE_UP, this.onInputTouchPC, this);

                // 确保输入框可交互
                this.input.enabled = true;
                this.input.node.active = true;

                console.log("PC端输入框事件监听已添加");
            }

            // 监听输入框回车事件
            this.input.node.on('editing-return', this.onInputReturn, this);
            // 监听输入框开始编辑事件
            this.input.node.on('editing-did-began', this.onInputDidBegan, this);
            // 监听输入框结束编辑事件
            this.input.node.on('editing-did-ended', this.onInputDidEnded, this);

            console.log("输入框事件监听器已设置完成");
        } else {
            console.error("❌ 输入框组件未找到！");
        }

        // 初始化消息标签
        if (this.messageLabel) {
            this.messageLabel.string = "";
            this.messageLabel.node.active = false;
        }

        // 绑定按钮事件
        if (this.checkmark) {
            this.checkmark.node.on(cc.Node.EventType.TOUCH_END, this.onClickCheckmark, this);
        }

        if (this.btn_close) {
            this.btn_close.node.on(cc.Node.EventType.TOUCH_END, this.onClickClose, this);
        }
    }

    protected onShow(): void {
        // 手机端直接弹出输入对话框，PC端显示输入框
        if (cc.sys.isMobile) {
            // 手机端：隐藏输入框，直接弹出输入对话框
            if (this.input) {
                this.input.node.active = false;
            }

            // 延迟弹出输入对话框，确保界面显示完成
            this.scheduleOnce(() => {
                this.showMobileInputDialog();
            }, 0.3);
        } else {
            // PC端：显示正常的输入框
            console.log("=== PC端显示输入框 ===");
            if (this.input) {
                console.log("设置PC端输入框属性");
                this.input.string = "";
                this.input.placeholder = "请输入6位邀请码";
                this.input.enabled = true;
                this.input.node.active = true;

                // 确保输入框在最上层
                this.input.node.zIndex = 999;

                // PC端多次尝试激活输入框
                this.scheduleOnce(() => {
                    this.tryActivateInputPC();
                }, 0.1);

                this.scheduleOnce(() => {
                    this.tryActivateInputPC();
                }, 0.5);

                this.scheduleOnce(() => {
                    this.tryActivateInputPC();
                }, 1.0);
            }
        }

        if (this.messageLabel) {
            this.messageLabel.string = "";
            this.messageLabel.node.active = false;
        }

        // 播放弹窗动画
        this.playShowAnimation();
    }

    protected onHide(): void {
        // 隐藏时清理
        this.hideMessage();
    }

    /**
     * 手机端输入框触摸事件
     */
    private onInputTouch() {
        if (cc.sys.isMobile) {
            // 强制激活输入框
            this.input.focus();
        }
    }

    /**
     * PC端输入框点击事件
     */
    private onInputTouchPC(event) {
        console.log("=== PC端输入框被点击 ===");
        console.log("事件类型:", event.type);
        console.log("输入框状态 - enabled:", this.input ? this.input.enabled : "输入框不存在");
        console.log("输入框状态 - active:", this.input ? this.input.node.active : "输入框不存在");

        if (!cc.sys.isMobile && this.input) {
            // 确保输入框可用
            this.input.enabled = true;
            this.input.node.active = true;

            // PC端强制激活输入框
            try {
                this.input.focus();
                console.log("✅ PC端输入框已激活");

                // 额外尝试：直接设置输入框为编辑状态
                if (this.input._impl && this.input._impl._elem) {
                    this.input._impl._elem.focus();
                    console.log("✅ 直接激活HTML输入元素");
                }
            } catch (error) {
                console.error("❌ 激活输入框失败:", error);
            }
        }
    }

    /**
     * 输入框结束编辑事件
     */
    private onInputDidEnded() {
        console.log("输入框结束编辑");
    }

    /**
     * 尝试激活PC端输入框
     */
    private tryActivateInputPC() {
        if (!this.input || cc.sys.isMobile) {
            return;
        }

        console.log("=== 尝试激活PC端输入框 ===");
        console.log("输入框状态:", {
            enabled: this.input.enabled,
            active: this.input.node.active,
            visible: this.input.node.opacity > 0,
            zIndex: this.input.node.zIndex
        });

        try {
            // 方法1: 使用Cocos Creator的focus方法
            this.input.focus();
            console.log("✅ 方法1: Cocos focus() 调用成功");

            // 方法2: 直接操作HTML元素
            if (this.input._impl && this.input._impl._elem) {
                this.input._impl._elem.focus();
                this.input._impl._elem.click();
                console.log("✅ 方法2: HTML元素 focus() 调用成功");
            }

            // 方法3: 模拟点击事件
            const clickEvent = new cc.Event.EventTouch([], false);
            this.input.node.dispatchEvent(clickEvent);
            console.log("✅ 方法3: 模拟点击事件发送成功");

        } catch (error) {
            console.error("❌ 激活输入框失败:", error);
        }
    }

    /**
     * PC端输入对话框（备用方案）
     */
    private showPCInputDialog() {
        console.log("=== 显示PC端输入对话框 ===");

        // 使用浏览器的prompt对话框
        if (typeof prompt !== 'undefined') {
            const code = prompt("输入框无法激活，请在此输入6位邀请码:");
            if (code && code.trim()) {
                console.log("用户通过prompt输入:", code);
                this.validateInvitationCodeDirect(code.trim());
            } else {
                console.log("用户取消了prompt输入");
                this.showMessage("请输入6位邀请码", false);
            }
        } else {
            console.error("浏览器不支持prompt，无法提供备用输入方案");
            this.showMessage("输入框无法激活，请刷新页面重试", false);
        }
    }

    /**
     * 输入框开始编辑事件
     */
    private onInputDidBegan() {
        // 输入框正常工作
    }

    /**
     * 手机端输入对话框（最常规的做法）
     */
    private showMobileInputDialog() {
        // 方法1：微信小游戏环境
        if (typeof wx !== 'undefined' && wx.showModal) {
            wx.showModal({
                title: '请输入6位邀请码',
                editable: true,
                placeholderText: '请输入6位邀请码',
                success: (res) => {
                    if (res.confirm && res.content) {
                        const code = res.content.trim();
                        this.validateInvitationCodeDirect(code);
                    } else {
                        // 用户取消，关闭界面
                        this.hideUI();
                    }
                },
                fail: () => {
                    // 失败时使用测试模式
                    this.useTestMode();
                }
            });
            return;
        }

        // 方法2：浏览器环境
        if (typeof prompt !== 'undefined') {
            const code = prompt("请输入6位邀请码:");
            if (code) {
                this.validateInvitationCodeDirect(code.trim());
            } else {
                this.hideUI();
            }
            return;
        }

        // 方法3：其他环境，使用测试模式
        this.useTestMode();
    }

    /**
     * 测试模式（当无法使用输入对话框时）
     */
    private useTestMode() {
        const testCodes = ["A7K9M2", "B8L3N4", "C9M4O5"];
        const randomCode = testCodes[Math.floor(Math.random() * testCodes.length)];

        this.showMessage(`测试模式：使用邀请码 ${randomCode}`, true);

        // 延迟验证，让用户看到消息
        this.scheduleOnce(() => {
            this.validateInvitationCodeDirect(randomCode);
        }, 1);
    }

    /**
     * 输入框回车事件处理
     */
    private onInputReturn() {
        this.validateInvitationCode();
    }

    /**
     * 确认按钮点击事件
     */
    public onClickCheckmark() {
        AudioMgr.playSound(AudioPath.CLICK);
        console.log("=== 确认按钮被点击 ===");

        if (cc.sys.isMobile) {
            // 手机端：直接弹出输入对话框
            this.showMobileInputDialog();
        } else {
            // PC端：验证输入框内容
            if (this.input && this.input.string && this.input.string.trim() !== "") {
                console.log("输入框有内容，进行验证:", this.input.string);
                this.validateInvitationCode();
            } else {
                console.log("输入框为空，直接使用prompt输入");
                // PC端输入框为空时，直接使用prompt作为主要方案
                this.showPCInputDialog();
            }
        }
    }

    /**
     * 关闭按钮点击事件
     */
    public onClickClose() {
        AudioMgr.playSound(AudioPath.CLICK);
        this.hideUI();
    }

    /**
     * 验证邀请码
     */
    private validateInvitationCode() {
        if (!this.input) {
            console.error("输入框未绑定");
            return;
        }

        const code = this.input.string.trim();
        this.validateInvitationCodeDirect(code);
    }

    /**
     * 直接验证邀请码（支持外部传入代码）
     */
    private validateInvitationCodeDirect(code: string) {
        // 检查输入长度
        if (code.length !== 6) {
            this.showMessage("请输入6位邀请码", false);
            return;
        }

        // 验证邀请码
        if (InvitationCodes.validateCode(code)) {
            this.onInvitationSuccess(code);
        } else {
            this.showMessage("邀请码错误，请重新输入", false);
        }
    }

    /**
     * 邀请码验证成功处理
     */
    private onInvitationSuccess(code: string) {
        // 设置VIP状态
        LocalData.isVipUser = true;
        LocalData.usedRedeemCode = code;

        // 显示成功消息
        this.showMessage("兑换码使用成功！VIP权限已激活", true);

        // 延迟关闭界面
        this.scheduleOnce(() => {
            this.hideUI();
        }, 2);

        // 发送VIP状态变化事件
        cc.systemEvent.emit('vipStatusChanged', true);
    }

    /**
     * 显示消息
     */
    private showMessage(message: string, isSuccess: boolean) {
        if (!this.messageLabel) {
            console.error("消息标签未绑定");
            return;
        }

        this.messageLabel.string = message;
        this.messageLabel.node.active = true;
        
        // 设置消息颜色
        if (isSuccess) {
            this.messageLabel.node.color = cc.Color.GREEN;
        } else {
            this.messageLabel.node.color = cc.Color.RED;
        }

        // 播放消息动画
        this.playMessageAnimation();
    }

    /**
     * 隐藏消息
     */
    private hideMessage() {
        if (this.messageLabel) {
            this.messageLabel.node.active = false;
        }
    }

    /**
     * 播放显示动画
     */
    private playShowAnimation() {
        console.log("=== 播放显示动画 ===");

        if (!this.panel) {
            console.warn("⚠️ panel节点未绑定，无法播放动画");
            return;
        }

        console.log("panel节点存在，开始动画");
        console.log("panel初始状态 - 位置:", this.panel.position, "缩放:", this.panel.scale);

        // 初始状态：缩放为0
        this.panel.scale = 0;

        // 缩放动画
        cc.tween(this.panel)
            .to(0.3, { scale: 1 }, { easing: 'backOut' })
            .call(() => {
                console.log("✅ 弹窗动画播放完成");
            })
            .start();
    }

    /**
     * 播放消息动画
     */
    private playMessageAnimation() {
        if (!this.messageLabel) return;

        // 淡入动画
        this.messageLabel.node.opacity = 0;
        cc.tween(this.messageLabel.node)
            .to(0.2, { opacity: 255 })
            .start();
    }

    /**
     * 销毁时清理事件监听
     */
    onDestroy() {
        if (this.input) {
            this.input.node.off('editing-return', this.onInputReturn, this);
            this.input.node.off('editing-did-began', this.onInputDidBegan, this);
            this.input.node.off('editing-did-ended', this.onInputDidEnded, this);

            // 清理触摸事件
            if (cc.sys.isMobile) {
                this.input.node.off(cc.Node.EventType.TOUCH_START, this.onInputTouch, this);
            } else {
                this.input.node.off(cc.Node.EventType.TOUCH_START, this.onInputTouchPC, this);
                this.input.node.off(cc.Node.EventType.TOUCH_END, this.onInputTouchPC, this);
                this.input.node.off(cc.Node.EventType.MOUSE_DOWN, this.onInputTouchPC, this);
                this.input.node.off(cc.Node.EventType.MOUSE_UP, this.onInputTouchPC, this);
            }
        }
        if (this.checkmark) {
            this.checkmark.node.off(cc.Node.EventType.TOUCH_END, this.onClickCheckmark, this);
        }
        if (this.btn_close) {
            this.btn_close.node.off(cc.Node.EventType.TOUCH_END, this.onClickClose, this);
        }
    }
}
